/**
 * Tavily API Connection Test
 * Test script to verify Tavily API connectivity and troubleshoot issues
 */

import { TavilySearchResults } from '@kaibanjs/tools';

export async function testTavilyConnection() {
  console.log('🔍 Testing Tavily API Connection...');
  
  const apiKey = process.env.TAVILY_API_KEY || '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';
  
  if (!apiKey || apiKey === 'your-tavily-api-key') {
    console.error('❌ Tavily API key not configured properly');
    return false;
  }
  
  console.log('✅ API Key found:', apiKey.substring(0, 10) + '...');
  
  try {
    const searchTool = new TavilySearchResults({
      apiKey: apiKey,
      maxResults: 3,
    });
    
    console.log('🔍 Attempting test search...');
    
    // Simple test search with correct parameter name
    const result = await searchTool.invoke({
      searchQuery: 'artificial intelligence 2025'
    });
    
    console.log('✅ Tavily search successful!');
    console.log('📊 Results:', result ? 'Found results' : 'No results');
    
    return true;
    
  } catch (error) {
    console.error('❌ Tavily search failed:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('ConnectTimeoutError')) {
        console.error('🌐 Network connectivity issue - check firewall/proxy settings');
      } else if (error.message.includes('401') || error.message.includes('403')) {
        console.error('🔑 API key authentication issue');
      } else if (error.message.includes('429')) {
        console.error('⏰ Rate limit exceeded');
      } else {
        console.error('🔧 Unknown error:', error.message);
      }
    }
    
    return false;
  }
}

// Export for use in other modules
export const tavilyConnectionStatus = {
  isConnected: false,
  lastChecked: null as Date | null,
  error: null as string | null,
};

// Function to check and update connection status
export async function updateTavilyStatus() {
  try {
    tavilyConnectionStatus.isConnected = await testTavilyConnection();
    tavilyConnectionStatus.lastChecked = new Date();
    tavilyConnectionStatus.error = null;
  } catch (error) {
    tavilyConnectionStatus.isConnected = false;
    tavilyConnectionStatus.lastChecked = new Date();
    tavilyConnectionStatus.error = error instanceof Error ? error.message : 'Unknown error';
  }
  
  return tavilyConnectionStatus;
}
